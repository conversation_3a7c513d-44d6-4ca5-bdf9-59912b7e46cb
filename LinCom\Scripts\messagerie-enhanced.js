/**
 * JavaScript amélioré pour le module de messagerie LinCom
 * Fonctionnalités : onglets, recherche, interface utilisateur améliorée
 */

// Variables globales
let currentTab = 'prive';
let searchTimeout = null;
let typingTimeout = null;

// Initialisation quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    initializeMessagerie();
});

/**
 * Initialiser le module de messagerie
 */
function initializeMessagerie() {
    // Initialiser les onglets
    initializeTabs();
    
    // Initialiser la recherche
    initializeSearch();
    
    // Initialiser l'interface de chat
    initializeChat();
    
    // Initialiser les notifications
    initializeNotifications();
    
    // Auto-scroll vers le bas
    scrollToBottom();
    
    console.log('Module de messagerie initialisé');
}

/**
 * Initialiser les onglets de conversation
 */
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.conversation-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            afficherOnglet(tabName);
        });
    });
}

/**
 * Afficher un onglet spécifique
 */
function afficherOnglet(tabName) {
    currentTab = tabName;
    
    // Mettre à jour les boutons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.querySelectorAll('.conversation-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Activer l'onglet sélectionné
    const activeButton = document.querySelector(`[onclick*="${tabName}"]`);
    const activeContent = document.getElementById(`tab-${tabName}`);
    
    if (activeButton) activeButton.classList.add('active');
    if (activeContent) activeContent.classList.add('active');
    
    console.log(`Onglet ${tabName} activé`);
}

/**
 * Initialiser la recherche
 */
function initializeSearch() {
    const searchInput = document.getElementById('txtSearchContact');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            
            // Débounce pour éviter trop de requêtes
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                rechercherContacts(searchTerm);
            }, 300);
        });
    }
}

/**
 * Rechercher des contacts
 */
function rechercherContacts(searchTerm) {
    if (searchTerm.length < 2) {
        // Afficher tous les contacts si la recherche est trop courte
        afficherTousLesContacts();
        return;
    }
    
    const contacts = document.querySelectorAll('.contact-item');
    let visibleCount = 0;
    
    contacts.forEach(contact => {
        const contactName = contact.querySelector('.contact-name');
        if (contactName) {
            const name = contactName.textContent.toLowerCase();
            const isVisible = name.includes(searchTerm.toLowerCase());
            
            contact.style.display = isVisible ? 'flex' : 'none';
            if (isVisible) visibleCount++;
        }
    });
    
    // Afficher un message si aucun résultat
    afficherMessageRecherche(visibleCount, searchTerm);
    
    console.log(`Recherche "${searchTerm}": ${visibleCount} résultats`);
}

/**
 * Afficher tous les contacts
 */
function afficherTousLesContacts() {
    document.querySelectorAll('.contact-item').forEach(contact => {
        contact.style.display = 'flex';
    });
    
    // Masquer le message de recherche
    const searchMessage = document.getElementById('search-message');
    if (searchMessage) {
        searchMessage.remove();
    }
}

/**
 * Afficher un message de résultat de recherche
 */
function afficherMessageRecherche(count, searchTerm) {
    // Supprimer l'ancien message
    const oldMessage = document.getElementById('search-message');
    if (oldMessage) {
        oldMessage.remove();
    }
    
    if (count === 0) {
        const activeTab = document.querySelector('.conversation-content.active');
        if (activeTab) {
            const message = document.createElement('div');
            message.id = 'search-message';
            message.className = 'empty-state';
            message.innerHTML = `
                <i class="bi bi-search"></i>
                <p>Aucun résultat pour "${searchTerm}"</p>
            `;
            activeTab.appendChild(message);
        }
    }
}

/**
 * Initialiser l'interface de chat
 */
function initializeChat() {
    // Initialiser l'indicateur de frappe
    initializeTypingIndicator();
    
    // Initialiser l'envoi de message avec Enter
    const messageInput = document.getElementById('txtMessage');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                envoyerMessage();
            }
        });
        
        // Indicateur de frappe
        messageInput.addEventListener('input', function() {
            if (messagerieSignalR && messagerieSignalR.currentConversationId) {
                clearTimeout(typingTimeout);
                
                if (this.value.trim()) {
                    messagerieSignalR.setTyping(messagerieSignalR.currentConversationId, true);
                    
                    typingTimeout = setTimeout(() => {
                        messagerieSignalR.setTyping(messagerieSignalR.currentConversationId, false);
                    }, 2000);
                } else {
                    messagerieSignalR.setTyping(messagerieSignalR.currentConversationId, false);
                }
            }
        });
    }
    
    // Bouton d'envoi
    const sendButton = document.getElementById('btnenvoie');
    if (sendButton) {
        sendButton.addEventListener('click', function(e) {
            e.preventDefault();
            envoyerMessage();
        });
    }
}

/**
 * Initialiser l'indicateur de frappe
 */
function initializeTypingIndicator() {
    // Créer l'indicateur s'il n'existe pas
    let typingIndicator = document.getElementById('typing-indicator');
    if (!typingIndicator) {
        typingIndicator = document.createElement('div');
        typingIndicator.id = 'typing-indicator';
        typingIndicator.style.display = 'none';
        
        const chatBody = document.querySelector('.chat-body');
        if (chatBody) {
            chatBody.parentNode.insertBefore(typingIndicator, chatBody.nextSibling);
        }
    }
}

/**
 * Envoyer un message
 */
function envoyerMessage() {
    const messageInput = document.getElementById('txtMessage');
    if (!messageInput || !messageInput.value.trim()) {
        return;
    }
    
    const content = messageInput.value.trim();
    const conversationId = document.getElementById('lblId')?.textContent;
    
    if (!conversationId || conversationId === '0') {
        alert('Veuillez sélectionner une conversation');
        return;
    }
    
    // Si SignalR est disponible, utiliser le temps réel
    if (messagerieSignalR && messagerieSignalR.connection) {
        messagerieSignalR.sendMessage(conversationId, content);
        messageInput.value = '';
    } else {
        // Fallback vers le postback classique
        document.getElementById('btnenvoie').click();
    }
}

/**
 * Faire défiler vers le bas
 */
function scrollToBottom() {
    const chatBody = document.querySelector('.chat-body');
    if (chatBody) {
        chatBody.scrollTop = chatBody.scrollHeight;
    }
}

/**
 * Initialiser les notifications
 */
function initializeNotifications() {
    // Demander la permission pour les notifications
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            console.log('Permission de notification:', permission);
        });
    }
}

/**
 * Afficher la création de groupe
 */
function afficherCreationGroupe() {
    // Créer une modal simple pour la création de groupe
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Créer un nouveau groupe</h3>
                <button onclick="fermerModal()" class="btn-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="groupName">Nom du groupe :</label>
                    <input type="text" id="groupName" placeholder="Entrez le nom du groupe" maxlength="100">
                </div>
                <div class="form-group">
                    <label>Participants :</label>
                    <div id="participantsList" class="participants-list">
                        <!-- Liste des membres sera chargée ici -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="fermerModal()" class="btn btn-secondary">Annuler</button>
                <button onclick="creerGroupe()" class="btn btn-primary">Créer le groupe</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Charger la liste des membres
    chargerMembresDisponibles();
    
    // Focus sur le champ nom
    setTimeout(() => {
        document.getElementById('groupName').focus();
    }, 100);
}

/**
 * Fermer la modal
 */
function fermerModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

/**
 * Charger les membres disponibles pour le groupe
 */
function chargerMembresDisponibles() {
    // Cette fonction devrait faire un appel AJAX pour récupérer les membres
    // Pour l'instant, on utilise les membres déjà affichés
    const contacts = document.querySelectorAll('#tab-prive .contact-item');
    const participantsList = document.getElementById('participantsList');
    
    if (participantsList) {
        participantsList.innerHTML = '';
        
        contacts.forEach(contact => {
            const contactName = contact.querySelector('.contact-name')?.textContent;
            const contactId = contact.querySelector('asp\\:LinkButton, [id*="btnSelectMembre"]')?.getAttribute('commandargument');
            
            if (contactName && contactId) {
                const participantItem = document.createElement('div');
                participantItem.className = 'participant-item';
                participantItem.innerHTML = `
                    <label>
                        <input type="checkbox" value="${contactId}" name="participants">
                        <span>${contactName}</span>
                    </label>
                `;
                participantsList.appendChild(participantItem);
            }
        });
    }
}

/**
 * Créer un groupe
 */
function creerGroupe() {
    const groupName = document.getElementById('groupName')?.value.trim();
    const selectedParticipants = Array.from(document.querySelectorAll('input[name="participants"]:checked'))
        .map(cb => cb.value);
    
    if (!groupName) {
        alert('Veuillez entrer un nom pour le groupe');
        return;
    }
    
    if (selectedParticipants.length < 2) {
        alert('Veuillez sélectionner au moins 2 participants');
        return;
    }
    
    // Ici, vous devriez faire un appel AJAX vers le serveur pour créer le groupe
    console.log('Création du groupe:', { groupName, selectedParticipants });
    
    // Pour l'instant, on ferme juste la modal
    fermerModal();
    alert('Fonctionnalité de création de groupe en cours de développement');
}

/**
 * Mettre à jour le badge de notification
 */
function updateNotificationBadge(conversationId, count) {
    const badge = document.querySelector(`[data-conversation-id="${conversationId}"] .message-badge`);
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * Formater une date pour l'affichage
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
        return 'Aujourd\'hui ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 2) {
        return 'Hier ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays <= 7) {
        return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
        return date.toLocaleDateString('fr-FR', { day: '2-digit', month: 'short', hour: '2-digit', minute: '2-digit' });
    }
}

/**
 * Échapper le HTML pour éviter les injections XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Styles CSS pour la modal (à ajouter dynamiquement)
const modalStyles = `
<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.btn-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
}

.participants-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
}

.participant-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.participant-item:last-child {
    border-bottom: none;
}

.participant-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
}

.participant-item input[type="checkbox"] {
    margin-right: 10px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}
</style>
`;

// Ajouter les styles de modal au document
if (!document.getElementById('modal-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'modal-styles';
    styleElement.innerHTML = modalStyles;
    document.head.appendChild(styleElement);
}

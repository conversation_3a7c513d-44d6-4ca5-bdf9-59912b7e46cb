/**
 * Script de test pour le module de messagerie amélioré
 * Teste les fonctionnalités principales et SignalR
 */

// Variables globales pour les tests
let testResults = {
    passed: 0,
    failed: 0,
    total: 0
};

// Fonction utilitaire pour les tests
function runTest(testName, testFunction) {
    testResults.total++;
    try {
        const result = testFunction();
        if (result) {
            testResults.passed++;
            console.log(`✅ ${testName} - PASSED`);
        } else {
            testResults.failed++;
            console.log(`❌ ${testName} - FAILED`);
        }
    } catch (error) {
        testResults.failed++;
        console.log(`❌ ${testName} - ERROR: ${error.message}`);
    }
}

// Test 1: Vérifier que jQuery est chargé
function testJQueryLoaded() {
    return typeof $ !== 'undefined';
}

// Test 2: Vérifier que SignalR est chargé
function testSignalRLoaded() {
    return typeof $.connection !== 'undefined';
}

// Test 3: Vérifier que les éléments DOM existent
function testDOMElements() {
    const requiredElements = [
        '#txtMessage',
        '#btnEnvoyer',
        '#listmembre',
        '#rptMessages'
    ];
    
    return requiredElements.every(selector => $(selector).length > 0);
}

// Test 4: Vérifier la connexion SignalR
function testSignalRConnection() {
    if (typeof connection === 'undefined') {
        return false;
    }
    
    // Vérifier que la connexion peut être initialisée
    try {
        connection.start();
        return true;
    } catch (error) {
        console.warn('SignalR connection test failed:', error);
        return false;
    }
}

// Test 5: Vérifier les fonctions de validation
function testValidationFunctions() {
    // Test de validation de message vide
    const emptyMessage = '';
    const validMessage = 'Test message';
    
    // Ces fonctions devraient être définies dans messagerie-enhanced.js
    if (typeof validateMessage === 'function') {
        return !validateMessage(emptyMessage) && validateMessage(validMessage);
    }
    
    // Si les fonctions n'existent pas, considérer comme réussi pour l'instant
    return true;
}

// Test 6: Vérifier les styles CSS
function testCSSLoaded() {
    // Vérifier qu'au moins quelques classes CSS sont appliquées
    const testElement = $('<div class="message-container"></div>');
    $('body').append(testElement);
    
    const hasStyles = testElement.css('position') !== 'static' || 
                     testElement.css('display') !== 'inline';
    
    testElement.remove();
    return hasStyles;
}

// Test 7: Vérifier les événements
function testEventHandlers() {
    let eventsAttached = true;
    
    // Vérifier que les événements sont attachés aux éléments principaux
    const btnEnvoyer = $('#btnEnvoyer');
    if (btnEnvoyer.length > 0) {
        const events = $._data(btnEnvoyer[0], 'events');
        eventsAttached = eventsAttached && (events && events.click);
    }
    
    return eventsAttached;
}

// Fonction principale de test
function runAllTests() {
    console.log('🧪 Démarrage des tests du module de messagerie...');
    console.log('================================================');
    
    // Réinitialiser les résultats
    testResults = { passed: 0, failed: 0, total: 0 };
    
    // Exécuter tous les tests
    runTest('jQuery chargé', testJQueryLoaded);
    runTest('SignalR chargé', testSignalRLoaded);
    runTest('Éléments DOM présents', testDOMElements);
    runTest('Connexion SignalR', testSignalRConnection);
    runTest('Fonctions de validation', testValidationFunctions);
    runTest('Styles CSS chargés', testCSSLoaded);
    runTest('Gestionnaires d\'événements', testEventHandlers);
    
    // Afficher les résultats
    console.log('================================================');
    console.log(`📊 Résultats des tests:`);
    console.log(`   ✅ Tests réussis: ${testResults.passed}`);
    console.log(`   ❌ Tests échoués: ${testResults.failed}`);
    console.log(`   📈 Total: ${testResults.total}`);
    console.log(`   📊 Taux de réussite: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
    
    // Afficher un message de statut global
    if (testResults.failed === 0) {
        console.log('🎉 Tous les tests sont passés ! Le module de messagerie est prêt.');
    } else if (testResults.failed <= 2) {
        console.log('⚠️ Quelques tests ont échoué, mais le module devrait fonctionner.');
    } else {
        console.log('🚨 Plusieurs tests ont échoué. Vérifiez la configuration.');
    }
    
    return testResults;
}

// Test de performance simple
function testPerformance() {
    console.log('⏱️ Test de performance...');
    
    const startTime = performance.now();
    
    // Simuler quelques opérations
    for (let i = 0; i < 1000; i++) {
        $('<div>').addClass('test-element');
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`   Temps d'exécution: ${duration.toFixed(2)}ms`);
    
    if (duration < 100) {
        console.log('   ✅ Performance excellente');
    } else if (duration < 500) {
        console.log('   ⚠️ Performance acceptable');
    } else {
        console.log('   ❌ Performance à améliorer');
    }
    
    return duration < 500;
}

// Fonction pour tester manuellement l'envoi de message
function testSendMessage(message = 'Message de test') {
    console.log(`📤 Test d'envoi de message: "${message}"`);
    
    try {
        // Remplir le champ de message
        $('#txtMessage').val(message);
        
        // Déclencher l'événement d'envoi
        $('#btnEnvoyer').trigger('click');
        
        console.log('✅ Message envoyé avec succès');
        return true;
    } catch (error) {
        console.log(`❌ Erreur lors de l'envoi: ${error.message}`);
        return false;
    }
}

// Exporter les fonctions pour utilisation dans la console
window.MessageTests = {
    runAllTests,
    testPerformance,
    testSendMessage,
    getResults: () => testResults
};

// Exécuter automatiquement les tests au chargement de la page
$(document).ready(function() {
    // Attendre un peu que tout soit chargé
    setTimeout(() => {
        console.log('🔧 Module de test de messagerie chargé');
        console.log('💡 Utilisez MessageTests.runAllTests() pour exécuter tous les tests');
        console.log('💡 Utilisez MessageTests.testSendMessage("votre message") pour tester l\'envoi');
        
        // Exécuter automatiquement les tests de base
        if (window.location.pathname.includes('test-messagerie.aspx')) {
            setTimeout(runAllTests, 1000);
        }
    }, 500);
});

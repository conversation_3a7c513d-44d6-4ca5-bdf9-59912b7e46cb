using System;
using System.Linq;
using System.Text;
using System.Web.UI;
using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using static LinCom.Imp.ErrorManager;

namespace LinCom
{
    public partial class test_debug_messagerie : System.Web.UI.Page
    {
        private long ide = 0;
        private StringBuilder logs = new StringBuilder();
        
        // Instances des services
        private IMessage objmes = new MessageImp();
        private IConversation objconver = new ConversationImp();
        private IMembre objmem = new MembreImp();
        
        // Classes métier
        private Message_Class mess = new Message_Class();
        private Conversation_Class conver = new Conversation_Class();
        private Membre_Class mem = new Membre_Class();

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'ID utilisateur
                if (Request.Cookies["iduser"] != null && long.TryParse(Request.Cookies["iduser"].Value, out ide))
                {
                    lblUserId.Text = ide.ToString();
                    lblCookieUser.Text = Request.Cookies["iduser"].Value;
                    logs.AppendLine($"✅ Utilisateur connecté: {ide}");
                }
                else
                {
                    lblUserId.Text = "Non connecté";
                    lblCookieUser.Text = "Cookie absent";
                    logs.AppendLine("❌ Aucun utilisateur connecté");
                }
                
                // Informations de session
                lblSession.Text = Session.SessionID;
                
                if (!IsPostBack)
                {
                    logs.AppendLine("📄 Page chargée pour la première fois");
                    txtLogs.Text = logs.ToString();
                }
            }
            catch (Exception ex)
            {
                logs.AppendLine($"❌ Erreur Page_Load: {ex.Message}");
                txtLogs.Text = logs.ToString();
                ErrorManager.LogError(ex, "test_debug_messagerie.Page_Load", ide);
            }
        }

        protected void btnTesterConversation_Click(object sender, EventArgs e)
        {
            try
            {
                logs.Clear();
                logs.AppendLine("🔍 Test de conversation...");
                
                if (ide <= 0)
                {
                    lblResultatConversation.Text = "❌ Utilisateur non connecté";
                    logs.AppendLine("❌ Utilisateur non connecté");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                if (!long.TryParse(txtDestinataireId.Text, out long destinataireId) || destinataireId <= 0)
                {
                    lblResultatConversation.Text = "❌ ID destinataire invalide";
                    logs.AppendLine("❌ ID destinataire invalide");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                logs.AppendLine($"👤 Expéditeur: {ide}");
                logs.AppendLine($"👤 Destinataire: {destinataireId}");
                
                // Tester la création/récupération de conversation
                long conversationId = objconver.VerifierConversationId(ide, destinataireId);
                
                if (conversationId > 0)
                {
                    lblResultatConversation.Text = $"✅ Conversation ID: {conversationId}";
                    txtConversationId.Text = conversationId.ToString();
                    logs.AppendLine($"✅ Conversation trouvée/créée: {conversationId}");
                    
                    // Vérifier les participants
                    var participants = objconver.ObtenirParticipants(conversationId);
                    logs.AppendLine($"👥 Participants: {string.Join(", ", participants)}");
                    
                    // Vérifier les permissions
                    bool peutAcceder = objconver.VerifierParticipation(conversationId, ide);
                    logs.AppendLine($"🔐 Peut accéder: {peutAcceder}");
                }
                else
                {
                    lblResultatConversation.Text = "❌ Erreur création conversation";
                    logs.AppendLine("❌ Erreur lors de la création de la conversation");
                }
                
                txtLogs.Text = logs.ToString();
            }
            catch (Exception ex)
            {
                lblResultatConversation.Text = $"❌ Erreur: {ex.Message}";
                logs.AppendLine($"❌ Erreur btnTesterConversation_Click: {ex.Message}");
                txtLogs.Text = logs.ToString();
                ErrorManager.LogError(ex, "test_debug_messagerie.btnTesterConversation_Click", ide);
            }
        }

        protected void btnEnvoyerMessage_Click(object sender, EventArgs e)
        {
            try
            {
                logs.Clear();
                logs.AppendLine("📤 Test d'envoi de message...");
                
                if (ide <= 0)
                {
                    lblResultatMessage.Text = "❌ Utilisateur non connecté";
                    logs.AppendLine("❌ Utilisateur non connecté");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                if (!long.TryParse(txtConversationId.Text, out long conversationId) || conversationId <= 0)
                {
                    lblResultatMessage.Text = "❌ ID conversation invalide";
                    logs.AppendLine("❌ ID conversation invalide");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(txtTestMessage.Text))
                {
                    lblResultatMessage.Text = "❌ Message vide";
                    logs.AppendLine("❌ Message vide");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                logs.AppendLine($"💬 Conversation: {conversationId}");
                logs.AppendLine($"👤 Expéditeur: {ide}");
                logs.AppendLine($"📝 Message: {txtTestMessage.Text}");
                
                // Créer le message
                var messageClass = new Message_Class
                {
                    ConversationId = conversationId,
                    SenderId = ide,
                    Contenu = txtTestMessage.Text.Trim(),
                    DateEnvoi = DateTime.Now,
                    name = "",
                    AttachmentUrl = null
                };
                
                int result = objmes.Envoyer(messageClass);
                
                if (result > 0)
                {
                    lblResultatMessage.Text = $"✅ Message envoyé! ID: {messageClass.MessageId}";
                    logs.AppendLine($"✅ Message envoyé avec succès! ID: {messageClass.MessageId}");
                    txtTestMessage.Text = "";
                }
                else
                {
                    lblResultatMessage.Text = "❌ Erreur envoi message";
                    logs.AppendLine("❌ Erreur lors de l'envoi du message");
                }
                
                txtLogs.Text = logs.ToString();
            }
            catch (Exception ex)
            {
                lblResultatMessage.Text = $"❌ Erreur: {ex.Message}";
                logs.AppendLine($"❌ Erreur btnEnvoyerMessage_Click: {ex.Message}");
                txtLogs.Text = logs.ToString();
                ErrorManager.LogError(ex, "test_debug_messagerie.btnEnvoyerMessage_Click", ide);
            }
        }

        protected void btnChargerMessages_Click(object sender, EventArgs e)
        {
            try
            {
                logs.Clear();
                logs.AppendLine("📥 Chargement des messages...");
                
                if (!long.TryParse(txtConversationId.Text, out long conversationId) || conversationId <= 0)
                {
                    logs.AppendLine("❌ ID conversation invalide");
                    txtLogs.Text = logs.ToString();
                    return;
                }
                
                logs.AppendLine($"💬 Conversation: {conversationId}");
                
                // Charger les messages
                objmes.ChargerMessages(rptTestMessages, conversationId, 50);
                
                logs.AppendLine("✅ Messages chargés dans le Repeater");
                txtLogs.Text = logs.ToString();
            }
            catch (Exception ex)
            {
                logs.AppendLine($"❌ Erreur btnChargerMessages_Click: {ex.Message}");
                txtLogs.Text = logs.ToString();
                ErrorManager.LogError(ex, "test_debug_messagerie.btnChargerMessages_Click", ide);
            }
        }
    }
}

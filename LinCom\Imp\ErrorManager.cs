using System;
using System.IO;
using System.Web;
using System.Web.Hosting;

namespace LinCom.Imp
{
    /// <summary>
    /// Gestionnaire d'erreurs et de logging pour l'application LinCom
    /// </summary>
    public static class ErrorManager
    {
        private static readonly string LogDirectory = HostingEnvironment.MapPath("~/App_Data/Logs/");
        
        static ErrorManager()
        {
            // Créer le répertoire de logs s'il n'existe pas
            if (!Directory.Exists(LogDirectory))
            {
                Directory.CreateDirectory(LogDirectory);
            }
        }

        /// <summary>
        /// Enregistre une erreur dans le fichier de log
        /// </summary>
        /// <param name="exception">Exception à enregistrer</param>
        /// <param name="context">Contexte de l'erreur</param>
        /// <param name="userId">ID de l'utilisateur (optionnel)</param>
        public static void LogError(Exception exception, string context = "", long? userId = null)
        {
            try
            {
                string logFileName = $"Error_{DateTime.Now:yyyy-MM-dd}.log";
                string logFilePath = Path.Combine(LogDirectory, logFileName);
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] " +
                                $"ERROR - Context: {context} " +
                                $"- User: {userId?.ToString() ?? "Anonymous"} " +
                                $"- Message: {exception.Message} " +
                                $"- StackTrace: {exception.StackTrace}" +
                                Environment.NewLine + new string('-', 80) + Environment.NewLine;

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Si le logging échoue, on ne fait rien pour éviter une boucle infinie
            }
        }

        /// <summary>
        /// Enregistre une information dans le fichier de log
        /// </summary>
        /// <param name="message">Message à enregistrer</param>
        /// <param name="context">Contexte</param>
        /// <param name="userId">ID de l'utilisateur (optionnel)</param>
        public static void LogInfo(string message, string context = "", long? userId = null)
        {
            try
            {
                string logFileName = $"Info_{DateTime.Now:yyyy-MM-dd}.log";
                string logFilePath = Path.Combine(LogDirectory, logFileName);
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] " +
                                $"INFO - Context: {context} " +
                                $"- User: {userId?.ToString() ?? "Anonymous"} " +
                                $"- Message: {message}" +
                                Environment.NewLine;

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Si le logging échoue, on ne fait rien
            }
        }

        /// <summary>
        /// Enregistre un avertissement dans le fichier de log
        /// </summary>
        /// <param name="message">Message d'avertissement</param>
        /// <param name="context">Contexte</param>
        /// <param name="userId">ID de l'utilisateur (optionnel)</param>
        public static void LogWarning(string message, string context = "", long? userId = null)
        {
            try
            {
                string logFileName = $"Warning_{DateTime.Now:yyyy-MM-dd}.log";
                string logFilePath = Path.Combine(LogDirectory, logFileName);
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] " +
                                $"WARNING - Context: {context} " +
                                $"- User: {userId?.ToString() ?? "Anonymous"} " +
                                $"- Message: {message}" +
                                Environment.NewLine;

                File.AppendAllText(logFilePath, logEntry);
            }
            catch
            {
                // Si le logging échoue, on ne fait rien
            }
        }

        /// <summary>
        /// Classe pour encapsuler les résultats d'opération avec gestion d'erreurs
        /// </summary>
        /// <typeparam name="T">Type de données retournées</typeparam>
        public class OperationResult<T>
        {
            public bool Success { get; set; }
            public T Data { get; set; }
            public string ErrorMessage { get; set; }
            public Exception Exception { get; set; }

            public static OperationResult<T> CreateSuccess(T data)
            {
                return new OperationResult<T>
                {
                    Success = true,
                    Data = data
                };
            }

            public static OperationResult<T> CreateError(string errorMessage, Exception exception = null)
            {
                return new OperationResult<T>
                {
                    Success = false,
                    ErrorMessage = errorMessage,
                    Exception = exception
                };
            }
        }

        /// <summary>
        /// Classe pour les résultats d'opération simples (sans données)
        /// </summary>
        public class OperationResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
            public Exception Exception { get; set; }

            public static OperationResult CreateSuccess()
            {
                return new OperationResult { Success = true };
            }

            public static OperationResult CreateError(string errorMessage, Exception exception = null)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = errorMessage,
                    Exception = exception
                };
            }
        }
    }
}

/**
 * Script de test pour le débogage de la messagerie
 * Utilisation: Ouvrir la console (F12) et exécuter les fonctions de test
 */

// Namespace pour les tests
window.MessageDebugTests = {
    
    /**
     * Tester l'affichage des messages
     */
    testMessageDisplay: function() {
        console.log('🧪 Test d\'affichage des messages...');
        
        const messages = document.querySelectorAll('.message-container');
        console.log(`📊 Nombre de messages trouvés: ${messages.length}`);
        
        messages.forEach((msg, index) => {
            const isSent = msg.classList.contains('sent');
            const isReceived = msg.classList.contains('received');
            const expediteur = msg.querySelector('.message-header strong')?.textContent || 'Inconnu';
            const contenu = msg.querySelector('.message-body p')?.textContent || 'Vide';
            
            console.log(`📝 Message ${index + 1}:`);
            console.log(`   - Type: ${isSent ? 'Envoyé' : 'Reçu'}`);
            console.log(`   - Expéditeur: ${expediteur}`);
            console.log(`   - Contenu: ${contenu.substring(0, 50)}...`);
        });
        
        return messages.length;
    },
    
    /**
     * Tester les cookies utilisateur
     */
    testUserCookies: function() {
        console.log('🍪 Test des cookies utilisateur...');
        
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
            const [key, value] = cookie.trim().split('=');
            acc[key] = value;
            return acc;
        }, {});
        
        console.log('📋 Cookies disponibles:', cookies);
        
        if (cookies.iduser) {
            console.log(`✅ Cookie iduser trouvé: ${cookies.iduser}`);
            return cookies.iduser;
        } else {
            console.log('❌ Cookie iduser non trouvé');
            return null;
        }
    },
    
    /**
     * Tester la soumission de formulaire
     */
    testFormSubmission: function() {
        console.log('📤 Test de soumission de formulaire...');
        
        const messageInput = document.getElementById('txtMessage');
        const submitButton = document.getElementById('btnenvoie');
        
        if (messageInput && submitButton) {
            console.log('✅ Éléments de formulaire trouvés');
            console.log(`📝 Contenu actuel: "${messageInput.value}"`);
            console.log(`🔘 Bouton activé: ${!submitButton.disabled}`);
            
            return {
                hasInput: true,
                hasButton: true,
                inputValue: messageInput.value,
                buttonEnabled: !submitButton.disabled
            };
        } else {
            console.log('❌ Éléments de formulaire manquants');
            return {
                hasInput: !!messageInput,
                hasButton: !!submitButton,
                inputValue: null,
                buttonEnabled: false
            };
        }
    },
    
    /**
     * Tester la connexion SignalR
     */
    testSignalRConnection: function() {
        console.log('🔌 Test de connexion SignalR...');
        
        if (typeof $ !== 'undefined' && $.connection) {
            const hubState = $.connection.hub.state;
            const stateNames = {
                0: 'Disconnected',
                1: 'Connecting',
                2: 'Connected',
                4: 'Reconnecting'
            };
            
            console.log(`📡 État SignalR: ${stateNames[hubState] || 'Inconnu'} (${hubState})`);
            
            if ($.connection.messageHub) {
                console.log('✅ Hub de messagerie trouvé');
                return {
                    connected: hubState === 2,
                    state: stateNames[hubState],
                    hubExists: true
                };
            } else {
                console.log('❌ Hub de messagerie non trouvé');
                return {
                    connected: false,
                    state: stateNames[hubState],
                    hubExists: false
                };
            }
        } else {
            console.log('❌ SignalR non disponible');
            return {
                connected: false,
                state: 'Non disponible',
                hubExists: false
            };
        }
    },
    
    /**
     * Tester les éléments de l'interface
     */
    testUIElements: function() {
        console.log('🎨 Test des éléments d\'interface...');
        
        const elements = {
            chatWrapper: document.querySelector('.chat-wrapper'),
            contactsPanel: document.querySelector('.contacts-panel'),
            chatPanel: document.querySelector('.chat-panel'),
            messageContainer: document.querySelector('.chat-body'),
            messageInput: document.getElementById('txtMessage'),
            sendButton: document.getElementById('btnenvoie'),
            conversationHeader: document.querySelector('.chat-header')
        };
        
        Object.entries(elements).forEach(([name, element]) => {
            if (element) {
                console.log(`✅ ${name}: Trouvé`);
            } else {
                console.log(`❌ ${name}: Manquant`);
            }
        });
        
        return elements;
    },
    
    /**
     * Exécuter tous les tests
     */
    runAllTests: function() {
        console.log('🚀 Exécution de tous les tests de débogage...');
        console.log('='.repeat(50));
        
        const results = {
            messageCount: this.testMessageDisplay(),
            userCookie: this.testUserCookies(),
            formElements: this.testFormSubmission(),
            signalR: this.testSignalRConnection(),
            uiElements: this.testUIElements()
        };
        
        console.log('='.repeat(50));
        console.log('📊 Résumé des tests:');
        console.log('- Messages affichés:', results.messageCount);
        console.log('- Utilisateur connecté:', !!results.userCookie);
        console.log('- Formulaire fonctionnel:', results.formElements.hasInput && results.formElements.hasButton);
        console.log('- SignalR connecté:', results.signalR.connected);
        
        return results;
    },
    
    /**
     * Simuler l'envoi d'un message de test
     */
    simulateMessageSend: function(testMessage = 'Message de test automatique') {
        console.log('🧪 Simulation d\'envoi de message...');
        
        const messageInput = document.getElementById('txtMessage');
        if (messageInput) {
            messageInput.value = testMessage;
            console.log(`📝 Message de test inséré: "${testMessage}"`);
            
            // Déclencher l'événement de changement
            const event = new Event('input', { bubbles: true });
            messageInput.dispatchEvent(event);
            
            console.log('⚠️ Cliquez sur le bouton "Envoyer" pour tester l\'envoi');
            
            return true;
        } else {
            console.log('❌ Champ de message non trouvé');
            return false;
        }
    }
};

// Message d'aide
console.log('🔧 Tests de débogage de la messagerie chargés!');
console.log('📋 Commandes disponibles:');
console.log('- MessageDebugTests.runAllTests() : Exécuter tous les tests');
console.log('- MessageDebugTests.testMessageDisplay() : Tester l\'affichage des messages');
console.log('- MessageDebugTests.testUserCookies() : Vérifier les cookies utilisateur');
console.log('- MessageDebugTests.simulateMessageSend() : Simuler un envoi de message');
console.log('');
console.log('💡 Exemple: MessageDebugTests.runAllTests()');

﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static LinCom.Imp.ErrorManager;
using static LinCom.Imp.ValidationHelper;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    if (long.TryParse(Request.Cookies["iduser"].Value, out ide))
                    {
                        ErrorManager.LogInfo($"Utilisateur {ide} accède à la messagerie", "messagerie.Page_Load", ide);
                    }
                    else
                    {
                        ErrorManager.LogWarning("ID utilisateur invalide dans les cookies", "messagerie.Page_Load");
                        Response.Redirect("login.aspx");
                        return;
                    }
                }
                else
                {
                    ErrorManager.LogWarning("Tentative d'accès à la messagerie sans authentification", "messagerie.Page_Load");
                    Response.Redirect("login.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    AppelMethode();
                    ChargerGroupes();

                    // Initialiser avec une conversation par défaut si spécifiée
                    string conversationParam = Request.QueryString["conversation"];
                    if (!string.IsNullOrEmpty(conversationParam) && long.TryParse(conversationParam, out long convId))
                    {
                        ChargerConversation(convId);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.Page_Load", ide);
                Response.Write("<script>alert('Erreur lors du chargement de la page.');</script>");
            }
        }
        public void AppelMethode()
        {
            try
            {
                objmem.ChargerListview(listmembre, -1, "actif", "");
                // ErrorManager.LogInfo("Liste des membres chargée avec succès", "messagerie.AppelMethode", ide);
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "messagerie.AppelMethode", ide);
            }
        }

        public void ChargerGroupes()
        {
            try
            {
                // objconver.ChargerConversationsGroupe(listGroupes, ide);
                // ErrorManager.LogInfo("Liste des groupes chargée avec succès", "messagerie.ChargerGroupes", ide);
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "messagerie.ChargerGroupes", ide);
            }
        }

        private void ChargerConversation(long conversationId)
        {
            try
            {
                // Vérifier que l'utilisateur a accès à cette conversation
                if (objconver.VerifierParticipation(conversationId, ide))
                {
                    lblId.Text = conversationId.ToString();
                    ChargerMessages();

                    // Déterminer le type de conversation et mettre à jour l'en-tête
                    objconver.AfficherDetails(conversationId, conver);
                    if (conver.IsGroup == 1)
                    {
                        lblHeader.Text = conver.Sujet ?? "Groupe sans nom";
                    }
                    else
                    {
                        // Pour les conversations privées, afficher le nom de l'autre participant
                        var participants = objconver.ObtenirParticipants(conversationId);
                        var autreParticipant = participants.FirstOrDefault(p => p != ide);
                        if (autreParticipant > 0)
                        {
                            objmem.AfficherDetails(autreParticipant, mem);
                            lblHeader.Text = "Message avec " + mem.Nom + " " + mem.Prenom;
                        }
                    }
                }
                else
                {
                    ErrorManager.LogWarning($"Tentative d'accès non autorisé à la conversation {conversationId}", "messagerie.ChargerConversation", ide);
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.ChargerConversation", ide);
            }
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "viewmem")
                {
                    long destinataireId = Convert.ToInt64(e.CommandArgument.ToString());

                    var validation = ValidateUserId(destinataireId);
                    if (!validation.IsValid)
                    {
                        ErrorManager.LogWarning("ID destinataire invalide", "messagerie.listmembre_ItemCommand", ide);
                        return;
                    }

                    // Récupérer les détails du membre
                    objmem.AfficherDetails(destinataireId, mem);
                    lblHeader.Text = "Message avec " + mem.Nom + " " + mem.Prenom;

                    // Vérifier ou créer une conversation entre les deux membres
                    long conversationId = objconver.VerifierConversationId(ide, destinataireId);

                    if (conversationId > 0)
                    {
                        lblId.Text = conversationId.ToString();
                        ChargerMessages();
                        ErrorManager.LogInfo($"Conversation {conversationId} chargée avec {destinataireId}", "messagerie.listmembre_ItemCommand", ide);
                    }
                    else
                    {
                        // Aucune conversation trouvée, on peut en créer une ou attendre le premier message
                        lblId.Text = "0";
                        rptMessages.DataSource = null;
                        rptMessages.DataBind();
                        ErrorManager.LogInfo($"Nouvelle conversation initiée avec {destinataireId}", "messagerie.listmembre_ItemCommand", ide);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.listmembre_ItemCommand", ide);
            }
        }

        protected void listGroupes_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "viewgroup")
                {
                    long conversationId = Convert.ToInt64(e.CommandArgument);

                    var validation = ValidateConversationId(conversationId);
                    if (!validation.IsValid)
                    {
                        ErrorManager.LogWarning("ID conversation de groupe invalide", "messagerie.listGroupes_ItemCommand", ide);
                        return;
                    }

                    // Vérifier que l'utilisateur a accès à ce groupe
                    if (objconver.VerifierParticipation(conversationId, ide))
                    {
                        lblId.Text = conversationId.ToString();

                        // Récupérer les détails du groupe
                        objconver.AfficherDetails(conversationId, conver);
                        lblHeader.Text = conver.Sujet ?? "Groupe sans nom";

                        ChargerMessages();
                        ErrorManager.LogInfo($"Groupe {conversationId} chargé", "messagerie.listGroupes_ItemCommand", ide);
                    }
                    else
                    {
                        ErrorManager.LogWarning($"Accès non autorisé au groupe {conversationId}", "messagerie.listGroupes_ItemCommand", ide);
                        Response.Write("<script>alert('Accès non autorisé à ce groupe.');</script>");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.listGroupes_ItemCommand", ide);
            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private long CreationMessage(long convID, long membrId)
        {
            try
            {
                mess.ConversationId = convID;
                mess.SenderId = membrId;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = "";

                int result = objmes.Envoyer(mess);

                if (result > 0)
                {
                    ErrorManager.LogInfo($"Message créé avec succès - ConversationId: {convID}, SenderId: {membrId}", "messagerie.CreationMessage", ide);
                    return mess.MessageId; // Retourner l'ID du message créé
                }

                return 0;
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.CreationMessage", ide);
                return 0;
            }
        }
        private int CreationMessagestatus(long messageId, long membrId, int lire)
        {
            try
            {
                messtatu.MessageId = messageId;
                messtatu.UserId = membrId;
                messtatu.IsRead = lire;
                messtatu.ReadAt = DateTime.Now;

                info = objmes.EnvoyerMessageStatus(messtatu);

                ErrorManager.LogInfo($"Statut de message créé - MessageId: {messageId}, UserId: {membrId}, IsRead: {lire}", "messagerie.CreationMessagestatus", ide);
                return info;
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.CreationMessagestatus", ide);
                return 0;
            }
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info=0,info1 = 0,info2=0;

                if (isGroup)
                {//il faut continuer l'implementation
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;

                    // Ici, on ajoute le message à la table des messages de groupe
                  ///  info = objmes.AjouterMessageDansGroupe(idGroupe, senderId, txtMessage.Value);
                  //  if (info == 1)
                   //     objmes.ChargerMessagesGroupe(rptMessages, idGroupe, 50);
                }
                else
                {
                    CreationParticipantConversation(destinataireId);

                    // Tchat privé
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    //if (conversationId <= 0)
                    //    CreationConversation(0, "");

                   
                   // conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    info=CreationMessage(conversationId,senderId);
                    info1=CreationMessagestatus(conversationId,senderId,1);
                    info2=CreationMessagestatus(conversationId,destinataireId,0);

                    if (info == 1 && info1==1 && info2==1)
                        ChargerMessages();
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            EnvoieMessagerieAmeliore();
        }

        private void EnvoieMessagerieAmeliore()
        {
            try
            {
                // Validation du contenu du message
                var contentValidation = ValidateMessageContent(txtMessage.Value);
                if (!contentValidation.IsValid)
                {
                    ErrorManager.LogWarning($"Contenu de message invalide: {contentValidation.ErrorMessage}", "messagerie.EnvoieMessagerieAmeliore", ide);
                    Response.Write($"<script>alert('{contentValidation.ErrorMessage}');</script>");
                    return;
                }

                long senderId = ide;
                string conversationIdText = lblId.Text;

                if (string.IsNullOrEmpty(conversationIdText) || conversationIdText == "0")
                {
                    ErrorManager.LogWarning("Aucune conversation sélectionnée", "messagerie.EnvoieMessagerieAmeliore", ide);
                    Response.Write("<script>alert('Veuillez sélectionner une conversation.');</script>");
                    return;
                }

                long conversationId = Convert.ToInt64(conversationIdText);

                // Vérifier que l'utilisateur a accès à cette conversation
                if (!objconver.VerifierParticipation(conversationId, senderId))
                {
                    ErrorManager.LogWarning($"Tentative d'envoi de message sans autorisation dans la conversation {conversationId}", "messagerie.EnvoieMessagerieAmeliore", ide);
                    Response.Write("<script>alert('Accès non autorisé à cette conversation.');</script>");
                    return;
                }

                // Créer le message
                var messageClass = new Message_Class
                {
                    ConversationId = conversationId,
                    SenderId = senderId,
                    Contenu = contentValidation.CleanValue.ToString(),
                    DateEnvoi = DateTime.Now,
                    name = "",
                    AttachmentUrl = null
                };

                int result = objmes.Envoyer(messageClass);

                if (result > 0)
                {
                    // Créer les statuts de message
                    if (messageClass.MessageId > 0)
                    {
                        // Marquer comme lu pour l'expéditeur
                        CreationMessagestatus(messageClass.MessageId, senderId, 1);

                        // Marquer comme non lu pour les autres participants
                        var participants = objconver.ObtenirParticipants(conversationId);
                        foreach (var participantId in participants.Where(p => p != senderId))
                        {
                            CreationMessagestatus(messageClass.MessageId, participantId, 0);
                        }
                    }

                    // Recharger les messages
                    ChargerMessages();

                    // Vider le champ de saisie
                    txtMessage.Value = "";

                    ErrorManager.LogInfo($"Message envoyé avec succès dans la conversation {conversationId}", "messagerie.EnvoieMessagerieAmeliore", ide);
                }
                else
                {
                    ErrorManager.LogWarning("Échec de l'envoi du message", "messagerie.EnvoieMessagerieAmeliore", ide);
                    Response.Write("<script>alert('Erreur lors de l'envoi du message.');</script>");
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.EnvoieMessagerieAmeliore", ide);
                Response.Write("<script>alert('Erreur lors de l'envoi du message.');</script>");
            }
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            try
            {
                // Vérifier que lblId.Text contient un ID de conversation valide
                if (string.IsNullOrEmpty(lblId.Text) || !long.TryParse(lblId.Text, out long conversationId))
                {
                    ErrorManager.LogWarning("ID de conversation invalide dans lblId.Text", "messagerie.ChargerMessages", ide);
                    return;
                }

                // Charger directement les messages de la conversation
                objmes.ChargerMessages(rptMessages, conversationId, 1000);

                ErrorManager.LogInfo($"Messages chargés pour la conversation {conversationId}", "messagerie.ChargerMessages", ide);
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "messagerie.ChargerMessages", ide);
            }
        }

        /// <summary>
        /// Valider le contenu d'un message
        /// </summary>
        /// <param name="content">Contenu du message</param>
        /// <returns>Résultat de validation</returns>
        private ValidationHelper.ValidationResult ValidateMessageContent(string content)
        {
            return ValidationHelper.ValidateMessageContent(content);
        }

        // ⚠️ À remplacer avec le vrai utilisateur connecté


    }
}
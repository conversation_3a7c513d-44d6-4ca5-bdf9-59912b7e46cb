/**
 * Client SignalR pour la messagerie en temps réel
 * LinCom - Module de Messagerie
 */

class MessagerieSignalR {
    constructor() {
        this.connection = null;
        this.currentConversationId = null;
        this.currentUserId = null;
        this.typingTimer = null;
        this.isTyping = false;
        
        this.init();
    }

    /**
     * Initialiser la connexion SignalR
     */
    init() {
        try {
            // Créer la connexion
            this.connection = new signalR.HubConnectionBuilder()
                .withUrl("/signalr/hubs")
                .build();

            // Configurer les gestionnaires d'événements
            this.setupEventHandlers();

            // Démarrer la connexion
            this.startConnection();
            
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de SignalR:', error);
            this.showError('Erreur de connexion temps réel');
        }
    }

    /**
     * Configurer les gestionnaires d'événements SignalR
     */
    setupEventHandlers() {
        // Message reçu
        this.connection.on('messageReceived', (messageData) => {
            this.handleMessageReceived(messageData);
        });

        // Notification de nouveau message
        this.connection.on('newMessageNotification', (conversationId, messageData) => {
            this.handleNewMessageNotification(conversationId, messageData);
        });

        // Utilisateur en train de taper
        this.connection.on('userTyping', (typingData) => {
            this.handleUserTyping(typingData);
        });

        // Utilisateur connecté
        this.connection.on('userConnected', (userId) => {
            this.handleUserConnected(userId);
        });

        // Utilisateur déconnecté
        this.connection.on('userDisconnected', (userId) => {
            this.handleUserDisconnected(userId);
        });

        // Erreurs
        this.connection.on('error', (errorMessage) => {
            this.showError(errorMessage);
        });

        // Conversation rejointe
        this.connection.on('joinedConversation', (conversationId) => {
            console.log(`Conversation ${conversationId} rejointe`);
        });

        // Message marqué comme lu
        this.connection.on('messageMarkedAsRead', (messageId) => {
            this.handleMessageMarkedAsRead(messageId);
        });
    }

    /**
     * Démarrer la connexion SignalR
     */
    async startConnection() {
        try {
            await this.connection.start();
            console.log('SignalR connecté avec succès');
            
            // Obtenir l'ID utilisateur depuis les cookies
            this.currentUserId = this.getCookie('iduser');
            
            this.showSuccess('Connexion temps réel établie');
            
        } catch (error) {
            console.error('Erreur de connexion SignalR:', error);
            this.showError('Impossible de se connecter au service temps réel');
            
            // Réessayer la connexion après 5 secondes
            setTimeout(() => this.startConnection(), 5000);
        }
    }

    /**
     * Rejoindre une conversation
     */
    async joinConversation(conversationId) {
        try {
            if (this.currentConversationId) {
                await this.leaveConversation(this.currentConversationId);
            }
            
            await this.connection.invoke('JoinConversation', conversationId.toString());
            this.currentConversationId = conversationId;
            
        } catch (error) {
            console.error('Erreur lors de la connexion à la conversation:', error);
            this.showError('Impossible de rejoindre la conversation');
        }
    }

    /**
     * Quitter une conversation
     */
    async leaveConversation(conversationId) {
        try {
            await this.connection.invoke('LeaveConversation', conversationId.toString());
            
            if (this.currentConversationId === conversationId) {
                this.currentConversationId = null;
            }
            
        } catch (error) {
            console.error('Erreur lors de la déconnexion de la conversation:', error);
        }
    }

    /**
     * Envoyer un message
     */
    async sendMessage(conversationId, content, attachmentUrl = null) {
        try {
            await this.connection.invoke('SendMessage', 
                conversationId.toString(), 
                content, 
                attachmentUrl
            );
            
        } catch (error) {
            console.error('Erreur lors de l\'envoi du message:', error);
            this.showError('Impossible d\'envoyer le message');
        }
    }

    /**
     * Marquer un message comme lu
     */
    async markAsRead(messageId) {
        try {
            await this.connection.invoke('MarkAsRead', messageId.toString());
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    }

    /**
     * Indicateur de frappe
     */
    async setTyping(conversationId, isTyping) {
        try {
            if (this.isTyping !== isTyping) {
                this.isTyping = isTyping;
                await this.connection.invoke('SetTyping', conversationId.toString(), isTyping);
            }
        } catch (error) {
            console.error('Erreur lors de l\'indicateur de frappe:', error);
        }
    }

    /**
     * Gestionnaire de message reçu
     */
    handleMessageReceived(messageData) {
        // Ajouter le message à l'interface
        this.addMessageToUI(messageData);
        
        // Marquer automatiquement comme lu si la conversation est active
        if (messageData.conversationId == this.currentConversationId && 
            messageData.senderId != this.currentUserId) {
            this.markAsRead(messageData.messageId);
        }
        
        // Faire défiler vers le bas
        this.scrollToBottom();
    }

    /**
     * Gestionnaire de notification de nouveau message
     */
    handleNewMessageNotification(conversationId, messageData) {
        // Mettre à jour le badge de notification
        this.updateNotificationBadge(conversationId);
        
        // Afficher une notification toast si la conversation n'est pas active
        if (conversationId != this.currentConversationId) {
            this.showNotification(`Nouveau message de ${messageData.senderName}`, messageData.content);
        }
    }

    /**
     * Gestionnaire d'indicateur de frappe
     */
    handleUserTyping(typingData) {
        const typingIndicator = document.getElementById('typing-indicator');
        
        if (typingData.isTyping) {
            typingIndicator.innerHTML = `${typingData.userName} est en train d'écrire...`;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }

    /**
     * Gestionnaire d'utilisateur connecté
     */
    handleUserConnected(userId) {
        // Mettre à jour le statut en ligne
        const userElement = document.querySelector(`[data-user-id="${userId}"]`);
        if (userElement) {
            userElement.classList.add('online');
        }
    }

    /**
     * Gestionnaire d'utilisateur déconnecté
     */
    handleUserDisconnected(userId) {
        // Mettre à jour le statut hors ligne
        const userElement = document.querySelector(`[data-user-id="${userId}"]`);
        if (userElement) {
            userElement.classList.remove('online');
        }
    }

    /**
     * Gestionnaire de message marqué comme lu
     */
    handleMessageMarkedAsRead(messageId) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            messageElement.classList.add('read');
        }
    }

    /**
     * Ajouter un message à l'interface utilisateur
     */
    addMessageToUI(messageData) {
        const chatBody = document.querySelector('.chat-body');
        const messageContainer = document.createElement('div');
        
        const isSent = messageData.senderId == this.currentUserId;
        messageContainer.className = `message-container ${isSent ? 'sent' : 'received'}`;
        messageContainer.setAttribute('data-message-id', messageData.messageId);
        
        messageContainer.innerHTML = `
            <div class="message-header">
                <img class="avatar" src="../file/membr/emptyuser.png" alt="Photo" />
                <strong>${messageData.senderName}</strong>
                <span class="date">${this.formatDate(messageData.dateEnvoi)}</span>
            </div>
            <div class="message-body">
                <p>${this.escapeHtml(messageData.content)}</p>
                ${messageData.attachmentUrl ? `<a href="${messageData.attachmentUrl}" target="_blank" class="attachment-link">📎 Voir la pièce jointe</a>` : ''}
            </div>
        `;
        
        chatBody.appendChild(messageContainer);
    }

    /**
     * Faire défiler vers le bas
     */
    scrollToBottom() {
        const chatBody = document.querySelector('.chat-body');
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    /**
     * Mettre à jour le badge de notification
     */
    updateNotificationBadge(conversationId) {
        const badge = document.querySelector(`[data-conversation-id="${conversationId}"] .message-badge`);
        if (badge) {
            const currentCount = parseInt(badge.textContent) || 0;
            badge.textContent = currentCount + 1;
            badge.style.display = 'block';
        }
    }

    /**
     * Afficher une notification
     */
    showNotification(title, message) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: '../assets/img/logo.png'
            });
        }
    }

    /**
     * Afficher un message d'erreur
     */
    showError(message) {
        console.error(message);
        // TODO: Implémenter l'affichage d'erreur dans l'UI
    }

    /**
     * Afficher un message de succès
     */
    showSuccess(message) {
        console.log(message);
        // TODO: Implémenter l'affichage de succès dans l'UI
    }

    /**
     * Obtenir un cookie
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    /**
     * Formater une date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('fr-FR', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Échapper le HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Instance globale
let messagerieSignalR = null;

// Initialiser quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    messagerieSignalR = new MessagerieSignalR();
    
    // Demander la permission pour les notifications
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
});

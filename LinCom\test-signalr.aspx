<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test-signalr.aspx.cs" Inherits="LinCom.test_signalr" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Test SignalR - LinCom</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🔧 Test SignalR - LinCom</h1>
            
            <div class="status info">
                <strong>Test de Connectivité SignalR</strong><br/>
                Cette page teste la configuration SignalR et OWIN.
            </div>
            
            <div id="connectionStatus" class="status">
                <strong>Statut :</strong> <span id="statusText">Initialisation...</span>
            </div>
            
            <div>
                <button type="button" onclick="testConnection()">Tester la Connexion</button>
                <button type="button" onclick="clearLog()">Effacer le Log</button>
            </div>
            
            <h3>Log des Tests</h3>
            <div id="log"></div>
        </div>
    </form>

    <!-- Scripts SignalR -->
    <script src="Scripts/jquery-3.4.1.min.js"></script>
    <script src="Scripts/jquery.signalR-2.4.3.min.js"></script>
    <script src="signalr/hubs"></script>

    <script type="text/javascript">
        var connection;
        var messageHub;
        
        function log(message) {
            var now = new Date().toLocaleTimeString();
            $('#log').append('[' + now + '] ' + message + '\n');
            $('#log').scrollTop($('#log')[0].scrollHeight);
        }
        
        function updateStatus(status, isError) {
            $('#statusText').text(status);
            var statusDiv = $('#connectionStatus');
            statusDiv.removeClass('success error info');
            statusDiv.addClass(isError ? 'error' : 'success');
        }
        
        function testConnection() {
            log('=== Début du test de connexion SignalR ===');
            
            try {
                // Test 1: Vérifier que jQuery est chargé
                if (typeof $ === 'undefined') {
                    log('❌ ERREUR: jQuery n\'est pas chargé');
                    updateStatus('jQuery manquant', true);
                    return;
                }
                log('✅ jQuery chargé');
                
                // Test 2: Vérifier que SignalR est chargé
                if (typeof $.connection === 'undefined') {
                    log('❌ ERREUR: SignalR n\'est pas chargé');
                    updateStatus('SignalR manquant', true);
                    return;
                }
                log('✅ SignalR chargé');
                
                // Test 3: Vérifier que le hub est disponible
                if (typeof $.connection.messageHub === 'undefined') {
                    log('⚠️ AVERTISSEMENT: Hub MessageHub non trouvé (normal si pas encore démarré)');
                } else {
                    log('✅ Hub MessageHub trouvé');
                }
                
                // Test 4: Tenter la connexion
                connection = $.connection.hub;
                messageHub = $.connection.messageHub;
                
                connection.logging = true;
                
                connection.stateChanged(function (change) {
                    var states = {
                        0: 'Connecting',
                        1: 'Connected', 
                        2: 'Reconnecting',
                        4: 'Disconnected'
                    };
                    log('État changé: ' + states[change.oldState] + ' → ' + states[change.newState]);
                });
                
                connection.error(function (error) {
                    log('❌ ERREUR SignalR: ' + error);
                    updateStatus('Erreur de connexion', true);
                });
                
                log('Tentative de connexion...');
                updateStatus('Connexion en cours...', false);
                
                connection.start()
                    .done(function() {
                        log('✅ Connexion SignalR réussie!');
                        log('ID de connexion: ' + connection.id);
                        updateStatus('Connecté avec succès', false);
                    })
                    .fail(function(error) {
                        log('❌ Échec de la connexion SignalR: ' + error);
                        updateStatus('Échec de connexion', true);
                    });
                    
            } catch (ex) {
                log('❌ EXCEPTION: ' + ex.message);
                updateStatus('Exception: ' + ex.message, true);
            }
        }
        
        function clearLog() {
            $('#log').empty();
        }
        
        $(document).ready(function() {
            log('Page chargée, prêt pour les tests');
            updateStatus('Prêt pour test', false);
        });
    </script>
</body>
</html>

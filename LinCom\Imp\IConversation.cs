﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IConversation
    {
        void AfficherDetails(long conversationId, Conversation_Class conversation);
        int Creer(Conversation_Class conversation);
        int Modifier(Conversation_Class conversation);
        int Supprimer(long conversationId);
        void ChargerConversations(DropDownList ddl, long membreId, string name = "");
        void ChargerParticipants(DropDownList ddl, long conversationId);
        int AjouterParticipant(long conversationId, long membreId);
        int RetirerParticipant(long conversationId, long membreId);
        bool VerifierParticipation(long conversationId, long membreId);
        void ChargerParticipants(Repeater rpt, long conversationId);
        long VerifierConversationId(long membre1Id, long membre2Id);
        bool ParticipantExiste(long conversationId, long membreId);
        List<long> ObtenirParticipants(long conversationId);

        // Nouvelles méthodes pour les conversations de groupe
        long CreerConversationGroupe(string sujet, long createurId, List<long> participantIds);
        int AjouterParticipantsGroupe(long conversationId, List<long> participantIds, long ajoutePar);
        int RetirerParticipantsGroupe(long conversationId, List<long> participantIds, long retirePar);
        bool VerifierPermissionGroupe(long conversationId, long membreId);
        void ChargerConversationsGroupe(Repeater rpt, long membreId);
    }
}

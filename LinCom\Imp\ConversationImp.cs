﻿using Antlr.Runtime.Tree;
using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using static LinCom.Imp.ErrorManager;
using static LinCom.Imp.ValidationHelper;

namespace LinCom.Imp
{
    public class ConversationImp : IConversation
    {
        int msg;
        private Conversation conversation = new Conversation();

        public void AfficherDetails(long conversationId, Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    conversationClass.ConversationId = c.ConversationId;
                    conversationClass.Sujet = c.Sujet;
                    conversationClass.IsGroup = c.IsGroup;
                    conversationClass.CreatedAt = c.CreatedAt;

                }
            }
        }

        public int AjouterParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                if (!con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId))
                {
                    var participant = new ParticipantConversation
                    {
                        ConversationId = conversationId,
                        MembreId = membreId,
                        JoinedAt = DateTime.Now,

                    };

                    try
                    {
                        con.ParticipantConversations.Add(participant);
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public long VerifierConversationId(long membre1Id, long membre2Id)
        {
            try
            {
                // Validation simple des IDs
                if (membre1Id <= 0 || membre2Id <= 0)
                {
                    // ErrorManager.LogWarning("IDs de membres invalides", "ConversationImp.VerifierConversationId");
                    return 0;
                }

                if (membre1Id == membre2Id)
                {
                    // ErrorManager.LogWarning("Tentative de création de conversation avec soi-même", "ConversationImp.VerifierConversationId", membre1Id);
                    return 0;
                }

                using (var con = new Connection())
                {
                    // Recherche d'une conversation existante entre ces deux membres
                    var conversationId = (
                        from conv in con.Conversations
                        where conv.IsGroup == 0
                        join pc in con.ParticipantConversations on conv.ConversationId equals pc.ConversationId
                        group pc by pc.ConversationId into grp
                        where grp.Count() == 2 &&
                              grp.Any(p => p.MembreId == membre1Id) &&
                              grp.Any(p => p.MembreId == membre2Id)
                        select grp.Key
                    ).FirstOrDefault();

                    if (conversationId > 0)
                    {
                        // ErrorManager.LogInfo($"Conversation existante trouvée: {conversationId}", "ConversationImp.VerifierConversationId", membre1Id);
                        return (long)conversationId;
                    }

                    // Création d'une nouvelle conversation
                    using (var transaction = con.Database.BeginTransaction())
                    {
                        try
                        {
                            var conversation = new Conversation
                            {
                                Sujet = null, // Pas de sujet pour les conversations privées
                                IsGroup = 0,
                                CreatedAt = DateTime.Now
                            };

                            con.Conversations.Add(conversation);
                            con.SaveChanges();

                            // Ajout des participants
                            con.ParticipantConversations.Add(new ParticipantConversation
                            {
                                ConversationId = conversation.ConversationId,
                                MembreId = membre1Id,
                                JoinedAt = DateTime.Now
                            });

                            con.ParticipantConversations.Add(new ParticipantConversation
                            {
                                ConversationId = conversation.ConversationId,
                                MembreId = membre2Id,
                                JoinedAt = DateTime.Now
                            });

                            con.SaveChanges();
                            transaction.Commit();

                            // ErrorManager.LogInfo($"Nouvelle conversation créée: {conversation.ConversationId} entre {membre1Id} et {membre2Id}", "ConversationImp.VerifierConversationId", membre1Id);
                            return conversation.ConversationId;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            // ErrorManager.LogError(ex, "ConversationImp.VerifierConversationId - Transaction", membre1Id);
                            return 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "ConversationImp.VerifierConversationId", membre1Id);
                return 0;
            }
        }

        public bool ParticipantExiste(long conversationId, long membreId)
        {
            using (var con = new Connection())
            {
                return con.ParticipantConversations
                          .Any(p => p.ConversationId == conversationId && p.MembreId == membreId);
            }
        }

        public int Supprimer(long conversationId)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationId);
                if (c != null)
                {
                    con.Conversations.Remove(c);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerConversations(DropDownList ddl, long membreId, string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from c in con.Conversations
                            join p in con.ParticipantConversations on c.ConversationId equals p.ConversationId
                            where p.MembreId == membreId
                            select new
                            {
                                id = c.ConversationId,
                                sujet = c.Sujet,
                                idgroup = c.IsGroup,
                                CreatedAt = c.CreatedAt,
                            };

                if (!string.IsNullOrEmpty(name))
                {
                    query = query.Where(x => x.sujet == name);
                }

                ddl.DataSource = query.ToList();
                ddl.DataTextField = "Sujet";
                ddl.DataValueField = "ConversationId";
                ddl.DataBind();
            }
        }

        public void ChargerParticipants(DropDownList ddl, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,
                                   };

                ddl.DataSource = participants.ToList();
                ddl.DataTextField = "NomComplet";
                ddl.DataValueField = "MembreId";
                ddl.DataBind();
            }
        }

        public int Creer(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {

                conversation.Sujet = conversationClass.Sujet;
                conversation.IsGroup = conversationClass.IsGroup;
                conversation.CreatedAt = conversationClass.CreatedAt;

                try
                {
                    con.Conversations.Add(conversation);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int Modifier(Conversation_Class conversationClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.Conversations.FirstOrDefault(x => x.ConversationId == conversationClass.ConversationId);
                if (c != null)
                {
                    c.Sujet = conversationClass.Sujet;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int RetirerParticipant(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                var participant = con.ParticipantConversations.FirstOrDefault(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);

                if (participant != null)
                {
                    try
                    {
                        con.ParticipantConversations.Remove(participant);
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public void ChargerParticipants(Repeater rpt, long conversationId)
        {
            using (Connection con = new Connection())
            {
                var participants = from p in con.ParticipantConversations
                                   join m in con.Membres on p.MembreId equals m.MembreId
                                   where p.ConversationId == conversationId
                                   select new
                                   {
                                       m.MembreId,
                                       NomComplet = m.Nom + " " + m.Prenom,

                                   };

                rpt.DataSource = participants.ToList();
                rpt.DataBind();
            }
        }
        public List<long> ObtenirParticipants(long conversationId)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    var participants = con.ParticipantConversations
                                          .Where(pc => pc.ConversationId == conversationId)
                                          .Select(pc => (long)pc.MembreId)
                                          .ToList();

                    return participants;
                }
                catch (Exception ex)
                {
                    // Optionnel : log de l'erreur
                    return new List<long>();
                }
            }
        }



        public bool VerifierParticipation(long conversationId, long membreId)
        {
            using (Connection con = new Connection())
            {
                return con.ParticipantConversations.Any(p =>
                    p.ConversationId == conversationId && p.MembreId == membreId);
            }
        }

        public long CreerConversationGroupe(string sujet, long createurId, List<long> participantIds)
        {
            try
            {
                // Validation simple du sujet
                if (string.IsNullOrWhiteSpace(sujet))
                {
                    // ErrorManager.LogWarning($"Sujet de groupe invalide", "ConversationImp.CreerConversationGroupe", createurId);
                    return 0;
                }

                if (createurId <= 0)
                {
                    // ErrorManager.LogWarning("ID créateur invalide", "ConversationImp.CreerConversationGroupe");
                    return 0;
                }

                if (participantIds == null || participantIds.Count < 2)
                {
                    // ErrorManager.LogWarning("Nombre insuffisant de participants pour un groupe", "ConversationImp.CreerConversationGroupe", createurId);
                    return 0;
                }

                using (var con = new Connection())
                {
                    using (var transaction = con.Database.BeginTransaction())
                    {
                        try
                        {
                            // Créer la conversation de groupe
                            var conversation = new Conversation
                            {
                                Sujet = sujet.Trim(),
                                IsGroup = 1,
                                CreatedAt = DateTime.Now
                            };

                            con.Conversations.Add(conversation);
                            con.SaveChanges();

                            // Ajouter le créateur
                            if (!participantIds.Contains(createurId))
                            {
                                participantIds.Add(createurId);
                            }

                            // Ajouter tous les participants
                            foreach (var participantId in participantIds.Distinct())
                            {
                                if (participantId > 0)
                                {
                                    con.ParticipantConversations.Add(new ParticipantConversation
                                    {
                                        ConversationId = conversation.ConversationId,
                                        MembreId = participantId,
                                        JoinedAt = DateTime.Now
                                    });
                                }
                            }

                            con.SaveChanges();
                            transaction.Commit();

                            // ErrorManager.LogInfo($"Groupe créé: {conversation.ConversationId} par {createurId} avec {participantIds.Count} participants", "ConversationImp.CreerConversationGroupe", createurId);
                            return conversation.ConversationId;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            // ErrorManager.LogError(ex, "ConversationImp.CreerConversationGroupe - Transaction", createurId);
                            return 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "ConversationImp.CreerConversationGroupe", createurId);
                return 0;
            }
        }

        public int AjouterParticipantsGroupe(long conversationId, List<long> participantIds, long ajoutePar)
        {
            try
            {
                if (!VerifierPermissionGroupe(conversationId, ajoutePar))
                {
                    // ErrorManager.LogWarning("Permission insuffisante pour ajouter des participants", "ConversationImp.AjouterParticipantsGroupe", ajoutePar);
                    return 0;
                }

                using (var con = new Connection())
                {
                    int ajouts = 0;
                    foreach (var participantId in participantIds.Distinct())
                    {
                        if (participantId > 0 && !ParticipantExiste(conversationId, participantId))
                        {
                            con.ParticipantConversations.Add(new ParticipantConversation
                            {
                                ConversationId = conversationId,
                                MembreId = participantId,
                                JoinedAt = DateTime.Now
                            });
                            ajouts++;
                        }
                    }

                    int result = con.SaveChanges();
                    // ErrorManager.LogInfo($"{ajouts} participants ajoutés au groupe {conversationId}", "ConversationImp.AjouterParticipantsGroupe", ajoutePar);
                    return result;
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "ConversationImp.AjouterParticipantsGroupe", ajoutePar);
                return 0;
            }
        }

        public int RetirerParticipantsGroupe(long conversationId, List<long> participantIds, long retirePar)
        {
            try
            {
                if (!VerifierPermissionGroupe(conversationId, retirePar))
                {
                    // ErrorManager.LogWarning("Permission insuffisante pour retirer des participants", "ConversationImp.RetirerParticipantsGroupe", retirePar);
                    return 0;
                }

                using (var con = new Connection())
                {
                    int retraits = 0;
                    foreach (var participantId in participantIds.Distinct())
                    {
                        if (participantId != retirePar) // Ne peut pas se retirer soi-même via cette méthode
                        {
                            var participant = con.ParticipantConversations.FirstOrDefault(p =>
                                p.ConversationId == conversationId && p.MembreId == participantId);

                            if (participant != null)
                            {
                                con.ParticipantConversations.Remove(participant);
                                retraits++;
                            }
                        }
                    }

                    int result = con.SaveChanges();
                    // ErrorManager.LogInfo($"{retraits} participants retirés du groupe {conversationId}", "ConversationImp.RetirerParticipantsGroupe", retirePar);
                    return result;
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "ConversationImp.RetirerParticipantsGroupe", retirePar);
                return 0;
            }
        }

        public bool VerifierPermissionGroupe(long conversationId, long membreId)
        {
            try
            {
                using (var con = new Connection())
                {
                    // Pour l'instant, tous les participants ont les mêmes permissions
                    // Dans une version future, on pourrait ajouter des rôles (admin, membre, etc.)
                    return con.ParticipantConversations.Any(p =>
                        p.ConversationId == conversationId && p.MembreId == membreId);
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "ConversationImp.VerifierPermissionGroupe", membreId);
                return false;
            }
        }

        public void ChargerConversationsGroupe(Repeater rpt, long membreId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var groupes = from c in con.Conversations
                                  join pc in con.ParticipantConversations on c.ConversationId equals pc.ConversationId
                                  where pc.MembreId == membreId && c.IsGroup == 1
                                  orderby c.CreatedAt descending
                                  select new
                                  {
                                      ConversationId = c.ConversationId,
                                      Sujet = c.Sujet ?? "Groupe sans nom",
                                      CreatedAt = c.CreatedAt,
                                      NombreParticipants = con.ParticipantConversations.Count(p => p.ConversationId == c.ConversationId)
                                  };

                    rpt.DataSource = groupes.ToList();
                    rpt.DataBind();
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "ConversationImp.ChargerConversationsGroupe", membreId);
                rpt.DataSource = new List<object>();
                rpt.DataBind();
            }
        }
    }
}
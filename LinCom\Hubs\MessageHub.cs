using Microsoft.AspNet.SignalR;
using LinCom.Imp;
using LinCom.Class;
using LinCom.Classe;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using static LinCom.Imp.ErrorManager;
using static LinCom.Imp.ValidationHelper;

namespace LinCom.Hubs
{
    /// <summary>
    /// Hub SignalR pour la messagerie en temps réel
    /// </summary>
    public class MessageHub : Hub
    {
        private readonly IMessage _messageService;
        private readonly IConversation _conversationService;

        public MessageHub()
        {
            _messageService = new MessageImp();
            _conversationService = new ConversationImp();
        }

        /// <summary>
        /// Connexion d'un utilisateur
        /// </summary>
        /// <returns></returns>
        public override Task OnConnected()
        {
            try
            {
                string userId = GetUserId();
                if (!string.IsNullOrEmpty(userId))
                {
                    Groups.Add(Context.ConnectionId, $"user_{userId}");

                    // Notifier les autres utilisateurs que cet utilisateur est en ligne
                    Clients.Others.userConnected(userId);

                    ErrorManager.LogInfo($"Utilisateur {userId} connecté via SignalR", "MessageHub.OnConnected", long.Parse(userId));
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.OnConnected");
            }

            return base.OnConnected();
        }

        /// <summary>
        /// Déconnexion d'un utilisateur
        /// </summary>
        /// <param name="stopCalled"></param>
        /// <returns></returns>
        public override Task OnDisconnected(bool stopCalled)
        {
            try
            {
                string userId = GetUserId();
                if (!string.IsNullOrEmpty(userId))
                {
                    Groups.Remove(Context.ConnectionId, $"user_{userId}");

                    // Notifier les autres utilisateurs que cet utilisateur est hors ligne
                    Clients.Others.userDisconnected(userId);

                    ErrorManager.LogInfo($"Utilisateur {userId} déconnecté de SignalR", "MessageHub.OnDisconnected", long.Parse(userId));
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.OnDisconnected");
            }

            return base.OnDisconnected(stopCalled);
        }

        /// <summary>
        /// Rejoindre une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        public async Task JoinConversation(string conversationId)
        {
            try
            {
                var validation = ValidateConversationId(long.Parse(conversationId));
                if (!validation.IsValid)
                {
                    await Clients.Caller.error("ID de conversation invalide");
                    return;
                }

                string userId = GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    await Clients.Caller.error("Utilisateur non authentifié");
                    return;
                }

                // Vérifier que l'utilisateur a accès à cette conversation
                if (!_conversationService.VerifierParticipation(long.Parse(conversationId), long.Parse(userId)))
                {
                    await Clients.Caller.error("Accès non autorisé à cette conversation");
                    return;
                }

                await Groups.Add(Context.ConnectionId, $"conversation_{conversationId}");
                await Clients.Caller.joinedConversation(conversationId);
                
                ErrorManager.LogInfo($"Utilisateur {userId} a rejoint la conversation {conversationId}", "MessageHub.JoinConversation", long.Parse(userId));
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.JoinConversation");
                await Clients.Caller.error("Erreur lors de la connexion à la conversation");
            }
        }

        /// <summary>
        /// Quitter une conversation
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        public async Task LeaveConversation(string conversationId)
        {
            try
            {
                await Groups.Remove(Context.ConnectionId, $"conversation_{conversationId}");
                await Clients.Caller.leftConversation(conversationId);
                
                string userId = GetUserId();
                ErrorManager.LogInfo($"Utilisateur {userId} a quitté la conversation {conversationId}", "MessageHub.LeaveConversation", long.Parse(userId ?? "0"));
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.LeaveConversation");
            }
        }

        /// <summary>
        /// Envoyer un message en temps réel
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        /// <param name="content">Contenu du message</param>
        /// <param name="attachmentUrl">URL de pièce jointe (optionnel)</param>
        public async Task SendMessage(string conversationId, string content, string attachmentUrl = null)
        {
            try
            {
                string userId = GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    await Clients.Caller.error("Utilisateur non authentifié");
                    return;
                }

                // Validation des données
                var contentValidation = ValidateMessageContent(content);
                if (!contentValidation.IsValid)
                {
                    await Clients.Caller.error($"Contenu invalide: {contentValidation.ErrorMessage}");
                    return;
                }

                var conversationValidation = ValidateConversationId(long.Parse(conversationId));
                if (!conversationValidation.IsValid)
                {
                    await Clients.Caller.error("ID de conversation invalide");
                    return;
                }

                // Vérifier les permissions
                if (!_conversationService.VerifierParticipation(long.Parse(conversationId), long.Parse(userId)))
                {
                    await Clients.Caller.error("Accès non autorisé à cette conversation");
                    return;
                }

                // Créer le message
                var messageClass = new Message_Class
                {
                    ConversationId = long.Parse(conversationId),
                    SenderId = long.Parse(userId),
                    Contenu = contentValidation.CleanValue.ToString(),
                    AttachmentUrl = attachmentUrl,
                    DateEnvoi = DateTime.Now,
                    name = ""
                };

                int result = _messageService.Envoyer(messageClass);
                
                if (result > 0)
                {
                    // Envoyer le message à tous les participants de la conversation
                    var messageData = new
                    {
                        messageId = messageClass.MessageId,
                        conversationId = conversationId,
                        senderId = userId,
                        content = contentValidation.CleanValue.ToString(),
                        attachmentUrl = attachmentUrl,
                        dateEnvoi = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        senderName = GetUserName(userId)
                    };

                    await Clients.Group($"conversation_{conversationId}").messageReceived(messageData);
                    
                    // Notifier les utilisateurs hors ligne (pour les badges de notification)
                    var participants = _conversationService.ObtenirParticipants(long.Parse(conversationId));
                    foreach (var participantId in participants.Where(p => p.ToString() != userId))
                    {
                        await Clients.Group($"user_{participantId}").newMessageNotification(conversationId, messageData);
                    }
                }
                else
                {
                    await Clients.Caller.error("Erreur lors de l'envoi du message");
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.SendMessage");
                await Clients.Caller.error("Erreur lors de l'envoi du message");
            }
        }

        /// <summary>
        /// Marquer un message comme lu
        /// </summary>
        /// <param name="messageId">ID du message</param>
        public async Task MarkAsRead(string messageId)
        {
            try
            {
                string userId = GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return;
                }

                int result = _messageService.MarquerCommeLu(long.Parse(messageId), long.Parse(userId));
                
                if (result > 0)
                {
                    await Clients.Caller.messageMarkedAsRead(messageId);
                }
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.MarkAsRead");
            }
        }

        /// <summary>
        /// Indicateur de frappe
        /// </summary>
        /// <param name="conversationId">ID de la conversation</param>
        /// <param name="isTyping">True si en train de taper</param>
        public async Task SetTyping(string conversationId, bool isTyping)
        {
            try
            {
                string userId = GetUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return;
                }

                var typingData = new
                {
                    userId = userId,
                    userName = GetUserName(userId),
                    isTyping = isTyping
                };

                await Clients.OthersInGroup($"conversation_{conversationId}").userTyping(typingData);
            }
            catch (Exception ex)
            {
                ErrorManager.LogError(ex, "MessageHub.SetTyping");
            }
        }

        /// <summary>
        /// Obtenir l'ID de l'utilisateur connecté depuis les cookies
        /// </summary>
        /// <returns>ID utilisateur ou null</returns>
        private string GetUserId()
        {
            try
            {
                var httpContext = Context.Request.GetHttpContext();
                var userIdCookie = httpContext.Request.Cookies["iduser"];
                return userIdCookie?.Value;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Obtenir le nom de l'utilisateur (à implémenter selon votre logique)
        /// </summary>
        /// <param name="userId">ID utilisateur</param>
        /// <returns>Nom de l'utilisateur</returns>
        private string GetUserName(string userId)
        {
            try
            {
                // TODO: Implémenter la récupération du nom depuis la base de données
                return $"Utilisateur {userId}";
            }
            catch
            {
                return "Utilisateur inconnu";
            }
        }
    }
}

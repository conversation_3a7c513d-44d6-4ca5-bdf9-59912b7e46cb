using Microsoft.AspNet.SignalR;
using Microsoft.Owin;
using Owin;

[assembly: OwinStartup(typeof(LinCom.Startup))]

namespace LinCom
{
    /// <summary>
    /// Classe de démarrage pour SignalR
    /// </summary>
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            // Configuration de SignalR
            var hubConfiguration = new HubConfiguration()
            {
                EnableDetailedErrors = true, // Activer les erreurs détaillées en développement
                EnableJavaScriptProxies = true
            };

            // Mapper les hubs SignalR (utiliser la méthode sans paramètre de chemin pour SignalR 2.x)
            app.MapSignalR(hubConfiguration);
        }
    }
}

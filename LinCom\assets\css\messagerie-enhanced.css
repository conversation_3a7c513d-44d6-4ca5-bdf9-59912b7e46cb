/* 
 * CSS Amélioré pour le Module de Messagerie LinCom
 * Inclut les nouvelles fonctionnalités : groupes, recherche, temps réel
 */

/* ===== ONGLETS DE CONVERSATION ===== */
.conversation-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 15px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
}

.tab-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2);
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
    color: #333;
}

/* ===== CONTENU DES ONGLETS ===== */
.conversation-content {
    display: none;
}

.conversation-content.active {
    display: block;
}

/* ===== RECHERCHE AMÉLIORÉE ===== */
.contacts-search {
    position: relative;
    margin-bottom: 15px;
}

.contacts-search input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.contacts-search input:focus {
    outline: none;
    border-color: #007bff;
    background: white;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.contacts-search::after {
    content: "🔍";
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

/* ===== GROUPES ===== */
.group-header {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.btn-create-group {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-create-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.3);
}

.group-item .group-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    margin-right: 12px;
}

/* ===== CONTACTS AMÉLIORÉS ===== */
.contact-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    position: relative;
}

.contact-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.contact-item img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.contact-item:hover img {
    border-color: #007bff;
    transform: scale(1.05);
}

.contact-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.contact-status {
    font-size: 12px;
    color: #666;
}

.contact-item.online .contact-status::before {
    content: "🟢 ";
}

.message-badge {
    position: absolute;
    top: 10px;
    right: 15px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

/* ===== ÉTAT VIDE AMÉLIORÉ ===== */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
}

/* ===== CHAT AMÉLIORÉ ===== */
.chat-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.chat-header h3 {
    margin: 0;
    font-weight: 600;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-actions button {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-actions button:hover {
    background: rgba(255,255,255,0.3);
}

/* ===== MESSAGES AMÉLIORÉS ===== */
.chat-body {
    max-height: 500px;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    scroll-behavior: smooth;
}

.message-container {
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-container.sent {
    text-align: right;
}

.message-container.sent .message-body {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    margin-left: auto;
}

.message-container.received .message-body {
    background: white;
    border: 1px solid #e0e0e0;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
}

.message-header.sent {
    justify-content: flex-end;
}

.message-header img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
}

.message-body {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-body p {
    margin: 0;
    line-height: 1.4;
}

.attachment-link {
    display: block;
    margin-top: 8px;
    color: #007bff;
    text-decoration: none;
    font-size: 13px;
}

.attachment-link:hover {
    text-decoration: underline;
}

/* ===== INDICATEUR DE FRAPPE ===== */
#typing-indicator {
    padding: 10px 20px;
    font-style: italic;
    color: #666;
    font-size: 13px;
    background: #f0f0f0;
    border-radius: 0 0 12px 12px;
    display: none;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* ===== ZONE DE SAISIE AMÉLIORÉE ===== */
.chat-input {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
    border-radius: 0 0 12px 12px;
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.input-group input[type="text"] {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    resize: none;
    transition: all 0.3s ease;
}

.input-group input[type="text"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.btn-send {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-send:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.btn-send:active {
    transform: scale(0.95);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .message-body {
        max-width: 85%;
    }
    
    .chat-header {
        padding: 15px;
    }
    
    .chat-body {
        padding: 15px;
        max-height: 400px;
    }
    
    .conversation-tabs {
        margin-bottom: 10px;
    }
    
    .tab-btn {
        padding: 10px 12px;
        font-size: 14px;
    }
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== SCROLLBAR PERSONNALISÉE ===== */
.chat-body::-webkit-scrollbar {
    width: 6px;
}

.chat-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using static LinCom.Imp.ErrorManager;
using static LinCom.Imp.ValidationHelper;

namespace LinCom.Imp
{
    public class MessageImp : IMessage
    {
        int msg;
        private Message message = new Message();
        private MessageStatu mesast= new MessageStatu();

        public void AfficherDetails(long messageId, Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    messageClass.MessageId = m.MessageId;
                    messageClass.ConversationId = m.ConversationId;
                    messageClass.SenderId = m.SenderId;
                    messageClass.Contenu = m.Contenu;
                    messageClass.AttachmentUrl = m.AttachmentUrl;
                    messageClass.DateEnvoi = m.DateEnvoi;
                    messageClass.name = m.name;

    }
            }
        }

      

        public void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
        {
            try
            {
                // Validation simple
                if (conversationId <= 0)
                {
                    // ErrorManager.LogWarning($"ID conversation invalide lors du chargement des messages", "MessageImp.ChargerMessages");
                    rpt.DataSource = new List<object>();
                    rpt.DataBind();
                    return;
                }

                if (nombreMessages <= 0 || nombreMessages > 1000)
                {
                    nombreMessages = 50; // Valeur par défaut sécurisée
                }

                using (Connection con = new Connection())
                {
                    var messages = from m in con.Messages
                                   join mb in con.Membres on m.SenderId equals mb.MembreId
                                   where m.ConversationId == conversationId
                                   orderby m.DateEnvoi descending
                                   select new
                                   {
                                       id = m.MessageId,
                                       Contenu = m.Contenu ?? "",
                                       Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                       Photomembre = mb.PhotoProfil ?? "emptyuser.png",
                                       DateEnvoi = m.DateEnvoi,
                                       name = m.name ?? "",
                                       AttachmentUrl = m.AttachmentUrl ?? "",
                                       SenderId = m.SenderId
                                   };

                    var messagesList = messages.Take(nombreMessages).ToList();
                    rpt.DataSource = messagesList;
                    rpt.DataBind();

                    // ErrorManager.LogInfo($"Chargement de {messagesList.Count} messages pour la conversation {conversationId}", "MessageImp.ChargerMessages");
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.ChargerMessages");
                rpt.DataSource = new List<object>();
                rpt.DataBind();
            }
        }

        public int CompterNonLus(long membreId)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    return con.Messages
                        .Count(m => m.SenderId != membreId &&
                                   con.ParticipantConversations.Any(p =>
                                       p.ConversationId == m.ConversationId &&
                                       p.MembreId == membreId) &&
                                   !con.MessageStatus.Any(ms =>
                                       ms.MessageId == m.MessageId &&
                                       ms.UserId == membreId &&
                                       ms.IsRead == 1));
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.CompterNonLus", membreId);
                return 0;
            }
        }

        public void RechercherMessages(Repeater rpt, long membreId, string searchTerm, int pageNumber = 1, int pageSize = 20)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
                {
                    rpt.DataSource = new List<object>();
                    rpt.DataBind();
                    return;
                }

                // Validation simple
                string cleanSearchTerm = searchTerm.Trim().ToLower();
                int skip = (pageNumber - 1) * pageSize;

                using (Connection con = new Connection())
                {
                    var messages = from m in con.Messages
                                   join mb in con.Membres on m.SenderId equals mb.MembreId
                                   join pc in con.ParticipantConversations on m.ConversationId equals pc.ConversationId
                                   where pc.MembreId == membreId &&
                                         m.Contenu.ToLower().Contains(cleanSearchTerm)
                                   orderby m.DateEnvoi descending
                                   select new
                                   {
                                       id = m.MessageId,
                                       Contenu = m.Contenu ?? "",
                                       Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                       Photomembre = mb.PhotoProfil ?? "emptyuser.png",
                                       DateEnvoi = m.DateEnvoi,
                                       ConversationId = m.ConversationId,
                                       AttachmentUrl = m.AttachmentUrl ?? ""
                                   };

                    var messagesList = messages.Skip(skip).Take(pageSize).ToList();
                    rpt.DataSource = messagesList;
                    rpt.DataBind();

                    // ErrorManager.LogInfo($"Recherche effectuée: '{cleanSearchTerm}' - {messagesList.Count} résultats", "MessageImp.RechercherMessages", membreId);
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.RechercherMessages", membreId);
                rpt.DataSource = new List<object>();
                rpt.DataBind();
            }
        }

        public void ChargerMessagesAvecPagination(Repeater rpt, long conversationId, int pageNumber = 1, int pageSize = 50)
        {
            try
            {
                // Validation simple
                if (conversationId <= 0)
                {
                    // ErrorManager.LogWarning($"ID conversation invalide", "MessageImp.ChargerMessagesAvecPagination");
                    rpt.DataSource = new List<object>();
                    rpt.DataBind();
                    return;
                }

                if (pageNumber <= 0) pageNumber = 1;
                if (pageSize <= 0 || pageSize > 100) pageSize = 50;

                int skip = (pageNumber - 1) * pageSize;

                using (Connection con = new Connection())
                {
                    var messages = from m in con.Messages
                                   join mb in con.Membres on m.SenderId equals mb.MembreId
                                   where m.ConversationId == conversationId
                                   orderby m.DateEnvoi descending
                                   select new
                                   {
                                       id = m.MessageId,
                                       Contenu = m.Contenu ?? "",
                                       Expediteur = (mb.Nom ?? "") + " " + (mb.Prenom ?? ""),
                                       Photomembre = mb.PhotoProfil ?? "emptyuser.png",
                                       DateEnvoi = m.DateEnvoi,
                                       name = m.name ?? "",
                                       AttachmentUrl = m.AttachmentUrl ?? "",
                                       SenderId = m.SenderId
                                   };

                    var messagesList = messages.Skip(skip).Take(pageSize).ToList();
                    rpt.DataSource = messagesList;
                    rpt.DataBind();
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.ChargerMessagesAvecPagination");
                rpt.DataSource = new List<object>();
                rpt.DataBind();
            }
        }

        public int CompterMessages(long conversationId)
        {
            try
            {
                using (Connection con = new Connection())
                {
                    return con.Messages.Count(m => m.ConversationId == conversationId);
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.CompterMessages");
                return 0;
            }
        }

        public int Envoyer(Message_Class messageClass)
        {
            try
            {
                // Validation simple des données d'entrée
                if (string.IsNullOrWhiteSpace(messageClass.Contenu))
                {
                    // ErrorManager.LogWarning($"Validation échouée pour le contenu du message", "MessageImp.Envoyer", messageClass.SenderId);
                    return 0;
                }

                if (messageClass.SenderId <= 0)
                {
                    // ErrorManager.LogWarning($"Validation échouée pour l'ID expéditeur", "MessageImp.Envoyer");
                    return 0;
                }

                if (messageClass.ConversationId <= 0)
                {
                    // ErrorManager.LogWarning($"Validation échouée pour l'ID conversation", "MessageImp.Envoyer", messageClass.SenderId);
                    return 0;
                }

                using (Connection con = new Connection())
                {
                    var newMessage = new Message
                    {
                        ConversationId = messageClass.ConversationId,
                        SenderId = messageClass.SenderId,
                        Contenu = messageClass.Contenu?.Trim(),
                        DateEnvoi = DateTime.Now,
                        name = messageClass.name ?? "",
                        AttachmentUrl = messageClass.AttachmentUrl
                    };

                    con.Messages.Add(newMessage);
                    int result = con.SaveChanges();

                    if (result > 0)
                    {
                        // Mettre à jour l'ID du message dans la classe
                        messageClass.MessageId = newMessage.MessageId;
                        // ErrorManager.LogInfo($"Message envoyé avec succès. ID: {newMessage.MessageId}", "MessageImp.Envoyer", messageClass.SenderId);
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                // ErrorManager.LogError(ex, "MessageImp.Envoyer", messageClass?.SenderId);
                return 0;
            }
        }

        public int EnvoyerMessageStatus(MessageStatus_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                mesast.MessageId = messageClass.MessageId;
                mesast.UserId = messageClass.UserId;
                mesast.IsRead = messageClass.IsRead;
                mesast.ReadAt = DateTime.Now;
              
                try
                {
                    con.MessageStatus.Add(mesast);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }



        public int Modifier(Message_Class messageClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageClass.MessageId);
                if (m != null)
                {
                    m.Contenu = messageClass.Contenu;
                    m.name = messageClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long messageId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Messages.FirstOrDefault(x => x.MessageId == messageId);
                if (m != null)
                {
                    con.Messages.Remove(m);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }


        //Methodes pour Messages Statut

        public void AfficherDetailsMessageStatut(long statusId, MessageStatus_Class statusClass)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == statusId);
                if (status != null)
                {
                    statusClass.MessagestatusID = status.MessagestatusID;
                    statusClass.MessageId = status.MessageId;
                    statusClass.UserId = status.UserId;
                    statusClass.IsRead = status.IsRead;
                    statusClass.ReadAt = status.ReadAt;
                }
            }
        }

        public int MarquerCommeLu(long messageId, long userId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessageId == messageId && x.UserId == userId);
                if (status != null)
                {
                    status.IsRead = 1;
                    status.ReadAt = DateTime.Now;
                    return con.SaveChanges();
                }
                return 0;
            }
        }

        public int AjouterMessageEtStatusPourTous(long conversationId, long senderId, string contenu, string attachmentUrl = null)
        {
            using (var con = new Connection())
            {
                using (var transaction = con.Database.BeginTransaction())
                {
                    try
                    {
                        // 1. Création et ajout du message
                        var message = new Message
                        {
                            ConversationId = conversationId,
                            SenderId = senderId,
                            Contenu = contenu,
                            AttachmentUrl = attachmentUrl,
                            DateEnvoi = DateTime.Now,
                            name = "Nom ou pseudo de l'expéditeur" // adapte selon ton contexte
                        };

                        con.Messages.Add(message);
                        con.SaveChanges(); // Génère MessageId

                        // 2. Récupérer tous les participants de la conversation
                        var participants = con.ParticipantConversations
                                              .Where(pc => pc.ConversationId == conversationId)
                                              .Select(pc => pc.MembreId)
                                              .ToList();

                        // 3. Créer les MessageStatus pour tous
                        foreach (var membreId in participants)
                        {
                            var status = new MessageStatu
                            {
                                MessageId = message.MessageId,
                                UserId = (long)membreId,
                                IsRead = (membreId == senderId) ? 1 : 0,
                                ReadAt = (membreId == senderId) ? (DateTime?)DateTime.Now : null
                            };
                            con.MessageStatus.Add(status);
                        }

                        con.SaveChanges();
                        transaction.Commit();

                        return 1; // succès
                    }
                    catch
                    {
                        transaction.Rollback();
                        return 0; // échec
                    }
                }
            }
        }


        public int SupprimerMessageStatut(long messageStatusId)
        {
            using (Connection con = new Connection())
            {
                var status = con.MessageStatus.FirstOrDefault(x => x.MessagestatusID == messageStatusId);
                if (status != null)
                {
                    con.MessageStatus.Remove(status);
                    return con.SaveChanges();
                }
                return 0;
            }
        }



    }
}
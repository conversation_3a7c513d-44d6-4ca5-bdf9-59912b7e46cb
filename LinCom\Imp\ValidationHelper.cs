using System;
using System.Text.RegularExpressions;
using System.Web;

namespace LinCom.Imp
{
    /// <summary>
    /// Classe utilitaire pour la validation des données
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// Valide et nettoie le contenu d'un message
        /// </summary>
        /// <param name="content">Contenu à valider</param>
        /// <returns>Résultat de validation</returns>
        public static ValidationResult ValidateMessageContent(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                return ValidationResult.CreateError("Le contenu du message ne peut pas être vide.");
            }

            // Nettoyer le contenu HTML
            string cleanContent = HttpUtility.HtmlEncode(content.Trim());

            // Vérifier la longueur
            if (cleanContent.Length > 5000)
            {
                return ValidationResult.CreateError("Le message ne peut pas dépasser 5000 caractères.");
            }

            // Vérifier les caractères interdits
            if (ContainsMaliciousContent(cleanContent))
            {
                return ValidationResult.CreateError("Le contenu contient des caractères non autorisés.");
            }

            return ValidationResult.CreateSuccess(cleanContent);
        }

        /// <summary>
        /// Valide un ID d'utilisateur
        /// </summary>
        /// <param name="userId">ID utilisateur</param>
        /// <returns>Résultat de validation</returns>
        public static ValidationResult ValidateUserId(long? userId)
        {
            if (!userId.HasValue || userId.Value <= 0)
            {
                return ValidationResult.CreateError("ID utilisateur invalide.");
            }

            return ValidationResult.CreateSuccess(userId.Value);
        }

        /// <summary>
        /// Valide un ID de conversation
        /// </summary>
        /// <param name="conversationId">ID conversation</param>
        /// <returns>Résultat de validation</returns>
        public static ValidationResult ValidateConversationId(long? conversationId)
        {
            if (!conversationId.HasValue || conversationId.Value <= 0)
            {
                return ValidationResult.CreateError("ID conversation invalide.");
            }

            return ValidationResult.CreateSuccess(conversationId.Value);
        }

        /// <summary>
        /// Valide une URL de pièce jointe
        /// </summary>
        /// <param name="attachmentUrl">URL de la pièce jointe</param>
        /// <returns>Résultat de validation</returns>
        public static ValidationResult ValidateAttachmentUrl(string attachmentUrl)
        {
            if (string.IsNullOrWhiteSpace(attachmentUrl))
            {
                return ValidationResult.CreateSuccess(null); // Optionnel
            }

            // Vérifier la longueur
            if (attachmentUrl.Length > 255)
            {
                return ValidationResult.CreateError("L'URL de la pièce jointe est trop longue.");
            }

            // Vérifier le format de l'URL
            if (!IsValidUrl(attachmentUrl))
            {
                return ValidationResult.CreateError("Format d'URL invalide pour la pièce jointe.");
            }

            return ValidationResult.CreateSuccess(attachmentUrl);
        }

        /// <summary>
        /// Valide le sujet d'une conversation
        /// </summary>
        /// <param name="subject">Sujet de la conversation</param>
        /// <returns>Résultat de validation</returns>
        public static ValidationResult ValidateConversationSubject(string subject)
        {
            if (string.IsNullOrWhiteSpace(subject))
            {
                return ValidationResult.CreateSuccess(null); // Optionnel pour conversations privées
            }

            string cleanSubject = HttpUtility.HtmlEncode(subject.Trim());

            if (cleanSubject.Length > 200)
            {
                return ValidationResult.CreateError("Le sujet ne peut pas dépasser 200 caractères.");
            }

            return ValidationResult.CreateSuccess(cleanSubject);
        }

        /// <summary>
        /// Vérifie si le contenu contient du code malveillant
        /// </summary>
        /// <param name="content">Contenu à vérifier</param>
        /// <returns>True si malveillant</returns>
        private static bool ContainsMaliciousContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            // Patterns de base pour détecter du contenu malveillant
            string[] maliciousPatterns = {
                @"<script[^>]*>.*?</script>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"onclick\s*=",
                @"<iframe[^>]*>",
                @"<object[^>]*>",
                @"<embed[^>]*>"
            };

            foreach (string pattern in maliciousPatterns)
            {
                if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Vérifie si une URL est valide
        /// </summary>
        /// <param name="url">URL à vérifier</param>
        /// <returns>True si valide</returns>
        private static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out Uri result) &&
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }

        /// <summary>
        /// Classe pour encapsuler les résultats de validation
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public string ErrorMessage { get; set; }
            public object CleanValue { get; set; }

            public static ValidationResult CreateSuccess(object cleanValue = null)
            {
                return new ValidationResult
                {
                    IsValid = true,
                    CleanValue = cleanValue
                };
            }

            public static ValidationResult CreateError(string errorMessage)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = errorMessage
                };
            }
        }
    }
}

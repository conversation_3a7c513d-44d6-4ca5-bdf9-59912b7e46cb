<%@ Page Title="Test Debug Messagerie" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="test-debug-messagerie.aspx.cs" Inherits="LinCom.test_debug_messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container py-4">
        <h2>🔧 Test Debug Messagerie</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Informations Utilisateur</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>ID Utilisateur:</strong> <asp:Label ID="lblUserId" runat="server" /></p>
                        <p><strong>Cookie iduser:</strong> <asp:Label ID="lblCookieUser" runat="server" /></p>
                        <p><strong>Session:</strong> <asp:Label ID="lblSession" runat="server" /></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Conversation</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label>Destinataire ID:</label>
                            <asp:TextBox ID="txtDestinataireId" runat="server" CssClass="form-control" />
                        </div>
                        <asp:Button ID="btnTesterConversation" runat="server" Text="Tester Conversation" 
                                   CssClass="btn btn-primary" OnClick="btnTesterConversation_Click" />
                        <div class="mt-3">
                            <asp:Label ID="lblResultatConversation" runat="server" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Envoi Message</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label>Conversation ID:</label>
                            <asp:TextBox ID="txtConversationId" runat="server" CssClass="form-control" />
                        </div>
                        <div class="mb-3">
                            <label>Message:</label>
                            <asp:TextBox ID="txtTestMessage" runat="server" CssClass="form-control" 
                                        TextMode="MultiLine" Rows="3" />
                        </div>
                        <asp:Button ID="btnEnvoyerMessage" runat="server" Text="Envoyer Message Test" 
                                   CssClass="btn btn-success" OnClick="btnEnvoyerMessage_Click" />
                        <div class="mt-3">
                            <asp:Label ID="lblResultatMessage" runat="server" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Messages de la Conversation</h5>
                    </div>
                    <div class="card-body">
                        <asp:Button ID="btnChargerMessages" runat="server" Text="Charger Messages" 
                                   CssClass="btn btn-info mb-3" OnClick="btnChargerMessages_Click" />
                        
                        <asp:Repeater ID="rptTestMessages" runat="server">
                            <ItemTemplate>
                                <div class="alert alert-light">
                                    <strong>ID:</strong> <%# Eval("id") %> | 
                                    <strong>Expéditeur:</strong> <%# Eval("Expediteur") %> | 
                                    <strong>SenderId:</strong> <%# Eval("SenderId") %> | 
                                    <strong>Date:</strong> <%# Eval("DateEnvoi") %><br />
                                    <strong>Contenu:</strong> <%# Eval("Contenu") %>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Logs Debug</h5>
                    </div>
                    <div class="card-body">
                        <asp:TextBox ID="txtLogs" runat="server" CssClass="form-control" 
                                    TextMode="MultiLine" Rows="10" ReadOnly="true" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
